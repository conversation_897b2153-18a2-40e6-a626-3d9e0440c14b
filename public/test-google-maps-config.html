<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Maps Configuration Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .warning { border-left: 4px solid #ffc107; }
        code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        .api-key {
            word-break: break-all;
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🗺️ Google Maps Configuration Test</h1>
    <p>This page tests if the Google Maps API key is properly configured in the JobbLogg application.</p>

    <div id="env-test" class="test-section">
        <h2>Environment Variable Test</h2>
        <p>Testing if <code>VITE_GOOGLE_MAPS_API_KEY</code> is available...</p>
        <div id="env-result"></div>
    </div>

    <div id="static-map-test" class="test-section">
        <h2>Static Map URL Generation Test</h2>
        <p>Testing Google Maps Static API URL generation...</p>
        <div id="static-map-result"></div>
    </div>

    <div id="directions-test" class="test-section">
        <h2>Directions URL Test</h2>
        <p>Testing Google Maps directions URL generation...</p>
        <div id="directions-result"></div>
    </div>

    <script type="module">
        // Test environment variable
        const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
        const envResult = document.getElementById('env-result');
        
        if (apiKey && apiKey.length > 0) {
            envResult.innerHTML = `
                <div class="success">
                    <strong>✅ Success!</strong> Google Maps API key is configured.
                    <div class="api-key">
                        <strong>API Key:</strong> ${apiKey.substring(0, 20)}...${apiKey.substring(apiKey.length - 4)}
                        <br><small>(Showing first 20 and last 4 characters for security)</small>
                    </div>
                </div>
            `;
            document.getElementById('env-test').classList.add('success');
        } else {
            envResult.innerHTML = `
                <div class="error">
                    <strong>❌ Error!</strong> Google Maps API key is not configured.
                    <p>Please add <code>VITE_GOOGLE_MAPS_API_KEY</code> to your <code>.env.local</code> file.</p>
                </div>
            `;
            document.getElementById('env-test').classList.add('error');
        }

        // Test static map URL generation
        const testAddress = {
            street: 'Karl Johans gate 1',
            postal: '0154',
            city: 'Oslo'
        };

        const staticMapResult = document.getElementById('static-map-result');
        if (apiKey) {
            const staticMapUrl = `https://maps.googleapis.com/maps/api/staticmap?center=${encodeURIComponent(testAddress.street + ', ' + testAddress.postal + ' ' + testAddress.city)}&zoom=15&size=400x200&maptype=roadmap&markers=color:red%7C${encodeURIComponent(testAddress.street + ', ' + testAddress.postal + ' ' + testAddress.city)}&key=${apiKey}`;
            
            staticMapResult.innerHTML = `
                <div class="success">
                    <strong>✅ Static Map URL Generated</strong>
                    <p><strong>Test Address:</strong> ${testAddress.street}, ${testAddress.postal} ${testAddress.city}</p>
                    <p><strong>Generated URL:</strong></p>
                    <code style="display: block; white-space: pre-wrap; margin: 10px 0;">${staticMapUrl}</code>
                    <img src="${staticMapUrl}" alt="Test map" style="max-width: 100%; border: 1px solid #ddd; border-radius: 4px; margin-top: 10px;" 
                         onload="this.parentElement.querySelector('.load-status').textContent = '✅ Map loaded successfully!'"
                         onerror="this.parentElement.querySelector('.load-status').textContent = '❌ Failed to load map. Check API key and billing.'; this.parentElement.querySelector('.load-status').style.color = 'red';">
                    <div class="load-status" style="margin-top: 10px; font-weight: bold;">Loading map...</div>
                </div>
            `;
            document.getElementById('static-map-test').classList.add('success');
        } else {
            staticMapResult.innerHTML = `
                <div class="error">
                    <strong>❌ Cannot test</strong> - API key not configured
                </div>
            `;
            document.getElementById('static-map-test').classList.add('error');
        }

        // Test directions URL
        const directionsResult = document.getElementById('directions-result');
        const directionsUrl = `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(testAddress.street + ', ' + testAddress.postal + ' ' + testAddress.city)}`;
        
        directionsResult.innerHTML = `
            <div class="success">
                <strong>✅ Directions URL Generated</strong>
                <p><strong>Test Address:</strong> ${testAddress.street}, ${testAddress.postal} ${testAddress.city}</p>
                <p><strong>Generated URL:</strong></p>
                <code style="display: block; white-space: pre-wrap; margin: 10px 0;">${directionsUrl}</code>
                <a href="${directionsUrl}" target="_blank" style="display: inline-block; background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-top: 10px;">
                    🗺️ Test Directions Link
                </a>
            </div>
        `;
        document.getElementById('directions-test').classList.add('success');

        // Add restart reminder if API key was just configured
        if (apiKey && apiKey.length > 0) {
            const restartNotice = document.createElement('div');
            restartNotice.className = 'test-section warning';
            restartNotice.innerHTML = `
                <h2>⚠️ Development Server Restart</h2>
                <p>If you just added the API key to <code>.env.local</code>, you may need to restart your development server for the changes to take effect:</p>
                <code>npm run dev</code>
                <p>Vite automatically loads environment variables, but changes require a restart.</p>
            `;
            document.body.appendChild(restartNotice);
        }
    </script>
</body>
</html>
