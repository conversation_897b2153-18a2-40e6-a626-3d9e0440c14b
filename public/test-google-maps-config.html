<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Maps Configuration Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .warning { border-left: 4px solid #ffc107; }
        code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        .api-key {
            word-break: break-all;
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🗺️ Google Maps Configuration Test</h1>
    <p>This page tests if the Google Maps API key is properly configured in the JobbLogg application.</p>

    <div id="env-test" class="test-section">
        <h2>Environment Variable Test</h2>
        <p>Testing if <code>VITE_GOOGLE_MAPS_API_KEY</code> is available...</p>
        <div id="env-result"></div>
    </div>

    <div id="static-map-test" class="test-section">
        <h2>Static Map URL Generation Test</h2>
        <p>Testing Google Maps Static API URL generation...</p>
        <div id="static-map-result"></div>
    </div>

    <div id="directions-test" class="test-section">
        <h2>Directions URL Test</h2>
        <p>Testing Google Maps directions URL generation...</p>
        <div id="directions-result"></div>
    </div>

    <div id="api-restrictions" class="test-section warning">
        <h2>🔧 API Key Configuration Guide</h2>
        <p>If you're getting 403 errors, check these settings in your <a href="https://console.cloud.google.com/apis/credentials" target="_blank">Google Cloud Console</a>:</p>

        <h3>1. Enable Required APIs</h3>
        <ul>
            <li>✅ <strong>Maps Static API</strong> (Required)</li>
            <li>🔧 <strong>Maps JavaScript API</strong> (Recommended)</li>
        </ul>

        <h3>2. API Key Restrictions</h3>
        <p><strong>Application restrictions:</strong></p>
        <ul>
            <li><strong>HTTP referrers (web sites)</strong> - Add these referrers:</li>
            <ul>
                <li><code>http://localhost:5173/*</code></li>
                <li><code>https://localhost:5173/*</code></li>
                <li><code>http://127.0.0.1:5173/*</code></li>
                <li><code>https://127.0.0.1:5173/*</code></li>
                <li>Your production domain when deployed</li>
            </ul>
        </ul>

        <p><strong>API restrictions:</strong></p>
        <ul>
            <li>Select "Restrict key" and choose:</li>
            <ul>
                <li>✅ Maps Static API</li>
                <li>🔧 Maps JavaScript API (if needed)</li>
            </ul>
        </ul>

        <h3>3. Billing</h3>
        <p>⚠️ Google Maps APIs require a billing account to be enabled, even for free tier usage.</p>
    </div>

    <script type="module">
        // Test environment variable
        const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
        const envResult = document.getElementById('env-result');
        
        if (apiKey && apiKey.length > 0) {
            envResult.innerHTML = `
                <div class="success">
                    <strong>✅ Success!</strong> Google Maps API key is configured.
                    <div class="api-key">
                        <strong>API Key:</strong> ${apiKey.substring(0, 20)}...${apiKey.substring(apiKey.length - 4)}
                        <br><small>(Showing first 20 and last 4 characters for security)</small>
                    </div>
                </div>
            `;
            document.getElementById('env-test').classList.add('success');
        } else {
            envResult.innerHTML = `
                <div class="error">
                    <strong>❌ Error!</strong> Google Maps API key is not configured.
                    <p>Please add <code>VITE_GOOGLE_MAPS_API_KEY</code> to your <code>.env.local</code> file.</p>
                </div>
            `;
            document.getElementById('env-test').classList.add('error');
        }

        // Test static map URL generation
        const testAddress = {
            street: 'Karl Johans gate 1',
            postal: '0154',
            city: 'Oslo'
        };

        const staticMapResult = document.getElementById('static-map-result');
        if (apiKey) {
            // Create URL using URLSearchParams to avoid double encoding
            const address = `${testAddress.street}, ${testAddress.postal} ${testAddress.city}, Norge`;
            const params = new URLSearchParams({
                center: address,
                zoom: '15',
                size: '400x200',
                maptype: 'roadmap',
                markers: `color:red|${address}`,
                key: apiKey
            });
            const staticMapUrl = `https://maps.googleapis.com/maps/api/staticmap?${params.toString()}`;

            staticMapResult.innerHTML = `
                <div class="success">
                    <strong>✅ Static Map URL Generated (Fixed Encoding)</strong>
                    <p><strong>Test Address:</strong> ${testAddress.street}, ${testAddress.postal} ${testAddress.city}</p>
                    <p><strong>Generated URL:</strong></p>
                    <code style="display: block; white-space: pre-wrap; margin: 10px 0; font-size: 12px;">${staticMapUrl}</code>
                    <img src="${staticMapUrl}" alt="Test map" style="max-width: 100%; border: 1px solid #ddd; border-radius: 4px; margin-top: 10px;"
                         onload="this.parentElement.querySelector('.load-status').textContent = '✅ Map loaded successfully! Encoding fix worked.'; this.parentElement.querySelector('.load-status').style.color = 'green';"
                         onerror="handleImageError(this)">
                    <div class="load-status" style="margin-top: 10px; font-weight: bold;">Loading map...</div>
                </div>
            `;
            document.getElementById('static-map-test').classList.add('success');
        } else {
            staticMapResult.innerHTML = `
                <div class="error">
                    <strong>❌ Cannot test</strong> - API key not configured
                </div>
            `;
            document.getElementById('static-map-test').classList.add('error');
        }

        // Error handler for detailed diagnostics
        window.handleImageError = function(img) {
            const loadStatus = img.parentElement.querySelector('.load-status');
            loadStatus.innerHTML = `
                <div style="color: red;">
                    <strong>❌ Failed to load map</strong><br>
                    <small>Possible causes:</small>
                    <ul style="text-align: left; margin: 10px 0; padding-left: 20px;">
                        <li>API key restrictions (check Google Cloud Console)</li>
                        <li>Maps Static API not enabled</li>
                        <li>Billing not enabled</li>
                        <li>HTTP referrer restrictions</li>
                        <li>IP address restrictions</li>
                    </ul>
                    <a href="https://console.cloud.google.com/apis/credentials" target="_blank" style="color: #007bff;">
                        🔧 Check API Key Settings
                    </a>
                </div>
            `;
        };

        // Test directions URL
        const directionsResult = document.getElementById('directions-result');
        const directionsUrl = `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(testAddress.street + ', ' + testAddress.postal + ' ' + testAddress.city)}`;
        
        directionsResult.innerHTML = `
            <div class="success">
                <strong>✅ Directions URL Generated</strong>
                <p><strong>Test Address:</strong> ${testAddress.street}, ${testAddress.postal} ${testAddress.city}</p>
                <p><strong>Generated URL:</strong></p>
                <code style="display: block; white-space: pre-wrap; margin: 10px 0;">${directionsUrl}</code>
                <a href="${directionsUrl}" target="_blank" style="display: inline-block; background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-top: 10px;">
                    🗺️ Test Directions Link
                </a>
            </div>
        `;
        document.getElementById('directions-test').classList.add('success');

        // Add restart reminder if API key was just configured
        if (apiKey && apiKey.length > 0) {
            const restartNotice = document.createElement('div');
            restartNotice.className = 'test-section warning';
            restartNotice.innerHTML = `
                <h2>⚠️ Development Server Restart</h2>
                <p>If you just added the API key to <code>.env.local</code>, you may need to restart your development server for the changes to take effect:</p>
                <code>npm run dev</code>
                <p>Vite automatically loads environment variables, but changes require a restart.</p>
            `;
            document.body.appendChild(restartNotice);
        }
    </script>
</body>
</html>
