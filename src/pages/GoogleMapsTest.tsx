import React, { useState, useEffect } from 'react';
import { generateStaticMapUrl, isGoogleMapsConfigured, formatAddress } from '../utils/googleMaps';
import { Card, TextStrong, TextMuted, PrimaryButton } from '../components/ui';
import { MapTypeComparison } from '../components/GoogleMaps';
import { testGoogleMapsAPI, diagnoseAPIIssues, generateTestMapUrl } from '../utils/debugGoogleMaps';

/**
 * Google Maps Configuration Test Page
 * Tests if Google Maps API is properly configured and working
 */
export const GoogleMapsTest: React.FC = () => {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [apiTestResult, setApiTestResult] = useState<any>(null);
  const [isTestingAPI, setIsTestingAPI] = useState(false);

  const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '';
  const isConfigured = isGoogleMapsConfigured();
  const diagnosis = diagnoseAPIIssues();

  // Test API on component mount
  useEffect(() => {
    if (isConfigured) {
      setIsTestingAPI(true);
      testGoogleMapsAPI().then(result => {
        setApiTestResult(result);
        setIsTestingAPI(false);
      });
    }
  }, [isConfigured]);

  // Test address
  const testAddress = {
    street: 'Karl Johans gate 1',
    postal: '0154',
    city: 'Oslo'
  };

  const staticMapUrl = isConfigured
    ? generateStaticMapUrl(testAddress.street, testAddress.postal, testAddress.city, {
        width: 400,
        height: 200,
        zoom: 16,
        mapType: 'hybrid'
      })
    : '';

  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoaded(false);
  };

  return (
    <div className="min-h-screen bg-jobblogg-neutral p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center py-8">
          <h1 className="text-3xl font-bold text-jobblogg-text-strong mb-2">
            🗺️ Google Maps Configuration Test
          </h1>
          <TextMuted>
            This page tests if the Google Maps API key is properly configured in JobbLogg
          </TextMuted>
        </div>

        {/* Environment Variable Test */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-jobblogg-text-strong mb-4">
            Environment Variable Test
          </h2>
          <TextMuted className="mb-4">
            Testing if <code className="bg-jobblogg-card px-2 py-1 rounded text-sm">VITE_GOOGLE_MAPS_API_KEY</code> is available...
          </TextMuted>
          
          {isConfigured ? (
            <div className="bg-jobblogg-success/10 border border-jobblogg-success/20 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-jobblogg-success">✅</span>
                <TextStrong className="text-jobblogg-success">Success!</TextStrong>
                <span className="text-jobblogg-text-medium">Google Maps API key is configured.</span>
              </div>
              <div className="bg-jobblogg-warning/10 border border-jobblogg-warning/20 rounded p-3 mt-3">
                <TextStrong className="text-sm">API Key:</TextStrong>
                <code className="block text-sm mt-1 break-all">
                  {apiKey.substring(0, 20)}...{apiKey.substring(apiKey.length - 4)}
                </code>
                <TextMuted className="text-xs mt-1">
                  (Showing first 20 and last 4 characters for security)
                </TextMuted>
              </div>
            </div>
          ) : (
            <div className="bg-jobblogg-error/10 border border-jobblogg-error/20 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-jobblogg-error">❌</span>
                <TextStrong className="text-jobblogg-error">Error!</TextStrong>
                <span className="text-jobblogg-text-medium">Google Maps API key is not configured.</span>
              </div>
              <TextMuted className="mt-2">
                Please add <code className="bg-jobblogg-card px-2 py-1 rounded text-sm">VITE_GOOGLE_MAPS_API_KEY</code> to your <code className="bg-jobblogg-card px-2 py-1 rounded text-sm">.env.local</code> file.
              </TextMuted>
            </div>
          )}
        </Card>

        {/* Static Map Test */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-jobblogg-text-strong mb-4">
            Static Map URL Generation Test
          </h2>
          <TextMuted className="mb-4">
            Testing Google Maps Static API URL generation...
          </TextMuted>

          {isConfigured ? (
            <div className="space-y-4">
              <div className="bg-jobblogg-success/10 border border-jobblogg-success/20 rounded-lg p-4">
                <TextStrong className="text-jobblogg-success mb-2 block">
                  ✅ Static Map URL Generated (Fixed Encoding)
                </TextStrong>
                <TextMuted className="mb-2">
                  <strong>Test Address:</strong> {formatAddress(testAddress.street, testAddress.postal, testAddress.city)}
                </TextMuted>
                
                <div className="mb-4">
                  <TextMuted className="mb-2"><strong>Generated URL:</strong></TextMuted>
                  <code className="block text-xs bg-jobblogg-card p-3 rounded break-all">
                    {staticMapUrl}
                  </code>
                </div>

                <div className="relative">
                  <img
                    src={staticMapUrl}
                    alt={`Map of ${formatAddress(testAddress.street, testAddress.postal, testAddress.city)}`}
                    className="w-full max-w-md border border-jobblogg-border rounded-lg"
                    onLoad={handleImageLoad}
                    onError={handleImageError}
                  />
                  
                  {!imageLoaded && !imageError && (
                    <div className="absolute inset-0 bg-jobblogg-neutral animate-pulse flex items-center justify-center rounded-lg">
                      <div className="w-8 h-8 border-2 border-jobblogg-primary border-t-transparent rounded-full animate-spin"></div>
                    </div>
                  )}
                </div>

                <div className="mt-4">
                  {imageLoaded && (
                    <div className="text-jobblogg-success font-medium">
                      ✅ Map loaded successfully! Encoding fix worked.
                    </div>
                  )}
                  {imageError && (
                    <div className="bg-jobblogg-error/10 border border-jobblogg-error/20 rounded-lg p-4">
                      <div className="text-jobblogg-error font-medium mb-2">
                        ❌ Failed to load map
                      </div>
                      <TextMuted className="text-sm mb-3">Possible causes:</TextMuted>
                      <ul className="text-sm text-jobblogg-text-medium space-y-1 ml-4">
                        <li>• API key restrictions (check Google Cloud Console)</li>
                        <li>• Maps Static API not enabled</li>
                        <li>• Billing not enabled</li>
                        <li>• HTTP referrer restrictions</li>
                        <li>• IP address restrictions</li>
                      </ul>
                      <a 
                        href="https://console.cloud.google.com/apis/credentials" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="inline-block mt-3 text-jobblogg-primary hover:text-jobblogg-primary-dark underline"
                      >
                        🔧 Check API Key Settings
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-jobblogg-error/10 border border-jobblogg-error/20 rounded-lg p-4">
              <TextStrong className="text-jobblogg-error">❌ Cannot test</TextStrong>
              <TextMuted className="ml-2">API key not configured</TextMuted>
            </div>
          )}
        </Card>

        {/* API Configuration Guide */}
        <Card className="p-6 bg-jobblogg-warning/5 border-jobblogg-warning/20">
          <h2 className="text-xl font-semibold text-jobblogg-text-strong mb-4">
            🔧 API Key Configuration Guide
          </h2>
          <TextMuted className="mb-4">
            If you're getting 403 errors, check these settings in your{' '}
            <a 
              href="https://console.cloud.google.com/apis/credentials" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-jobblogg-primary hover:text-jobblogg-primary-dark underline"
            >
              Google Cloud Console
            </a>:
          </TextMuted>

          <div className="space-y-6">
            <div>
              <h3 className="font-semibold text-jobblogg-text-strong mb-2">1. Enable Required APIs</h3>
              <ul className="space-y-1 ml-4 text-jobblogg-text-medium">
                <li>✅ <strong>Maps Static API</strong> (Required)</li>
                <li>🔧 <strong>Maps JavaScript API</strong> (Recommended)</li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-jobblogg-text-strong mb-2">2. API Key Restrictions</h3>
              <div className="ml-4 space-y-3">
                <div>
                  <TextStrong className="text-sm">Application restrictions:</TextStrong>
                  <TextMuted className="text-sm mt-1">HTTP referrers (web sites) - Add these referrers:</TextMuted>
                  <ul className="ml-4 mt-2 space-y-1">
                    <li><code className="text-xs bg-jobblogg-card px-2 py-1 rounded">http://localhost:5173/*</code></li>
                    <li><code className="text-xs bg-jobblogg-card px-2 py-1 rounded">https://localhost:5173/*</code></li>
                    <li><code className="text-xs bg-jobblogg-card px-2 py-1 rounded">http://127.0.0.1:5173/*</code></li>
                    <li><code className="text-xs bg-jobblogg-card px-2 py-1 rounded">https://127.0.0.1:5173/*</code></li>
                  </ul>
                </div>

                <div>
                  <TextStrong className="text-sm">API restrictions:</TextStrong>
                  <TextMuted className="text-sm mt-1">Select "Restrict key" and choose:</TextMuted>
                  <ul className="ml-4 mt-2 space-y-1 text-sm text-jobblogg-text-medium">
                    <li>✅ Maps Static API</li>
                    <li>🔧 Maps JavaScript API (if needed)</li>
                  </ul>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-jobblogg-text-strong mb-2">3. Billing</h3>
              <TextMuted className="ml-4">
                ⚠️ Google Maps APIs require a billing account to be enabled, even for free tier usage.
              </TextMuted>
            </div>
          </div>
        </Card>

        {/* Map Type Comparison */}
        <MapTypeComparison
          streetAddress={testAddress.street}
          postalCode={testAddress.postal}
          city={testAddress.city}
          className="mt-8"
        />

        {/* Navigation */}
        <div className="text-center py-6">
          <PrimaryButton
            onClick={() => window.location.href = '/create'}
            className="mr-4"
          >
            Test Project Wizard
          </PrimaryButton>
          <PrimaryButton
            onClick={() => window.location.href = '/'}
            variant="outline"
          >
            Back to Dashboard
          </PrimaryButton>
        </div>
      </div>
    </div>
  );
};

export default GoogleMapsTest;
