// Main chat components
export { ChatContainer } from './ChatContainer';
export { EmbeddedChatContainer } from './EmbeddedChatContainer';
export { MessageList } from './MessageList';
export { VirtualizedMessageList } from './VirtualizedMessageList';
export { MessageItem } from './MessageItem';
export { MessageInput } from './MessageInput';

// Supporting components
export { EmojiReactions } from './EmojiReactions';
export { FileAttachment } from './FileAttachment';
export { FilePreview } from './FilePreview';
export { TypingIndicator } from './TypingIndicator';
export { ConnectionStatus } from './ConnectionStatus';
export { DeliveryStatus } from './DeliveryStatus';

// Dashboard integration components
export { ChatStatsCard } from './ChatStatsCard';
export { ConversationCard } from './ConversationCard';

// Accessibility components
export {
  ScreenReaderAnnouncer,
  ChatScreenReaderContext,
  MessageThreadDescription,
  FileAttachmentDescription,
  ReactionDescription,
  useScreenReaderAnnouncements
} from './ScreenReaderAnnouncer';

// Re-export types for convenience
export type {
  Message,
  MessageWithDisplayInfo,
  MessageThread,
  ChatConversation,
  UnreadCounts,
  MessageFormData,
  ChatContainerProps,
  EmbeddedChatContainerProps,
  MessageListProps,
  MessageItemProps,
  MessageInputProps,
  EmojiReactionProps,
  FileAttachmentProps,
  ChatStatus,
  ChatError,
  TypingIndicator,
  OptimisticMessage
} from '../../types/chat';
