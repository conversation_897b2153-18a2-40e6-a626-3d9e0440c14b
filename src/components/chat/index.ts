// Main chat components
export { Chat<PERSON>ontainer } from './ChatContainer';
export { MessageList } from './MessageList';
export { MessageItem } from './MessageItem';
export { MessageInput } from './MessageInput';

// Supporting components
export { EmojiReactions } from './EmojiReactions';
export { FileAttachment } from './FileAttachment';
export { FilePreview } from './FilePreview';
export { TypingIndicator } from './TypingIndicator';
export { ConnectionStatus } from './ConnectionStatus';
export { DeliveryStatus } from './DeliveryStatus';

// Dashboard integration components
export { ChatStatsCard } from './ChatStatsCard';
export { ConversationCard } from './ConversationCard';

// Re-export types for convenience
export type {
  Message,
  MessageWithDisplayInfo,
  MessageThread,
  ChatConversation,
  UnreadCounts,
  MessageFormData,
  ChatContainerProps,
  MessageListProps,
  MessageItemProps,
  MessageInputProps,
  EmojiReactionProps,
  FileAttachmentProps,
  ChatStatus,
  ChatError,
  TypingIndicator,
  OptimisticMessage
} from '../../types/chat';
