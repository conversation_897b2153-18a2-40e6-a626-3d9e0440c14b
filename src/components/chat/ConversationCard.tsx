import React from 'react';
import { UnreadCounts } from '../../types/chat';

interface ConversationCardProps {
  /** Conversation data */
  conversation: UnreadCounts['conversationCounts'][0];
  /** Click handler for navigation */
  onClick: () => void;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Conversation card component for conversations overview
 * Shows project name, log description, and unread count
 * 
 * @example
 * ```tsx
 * <ConversationCard
 *   conversation={conversationData}
 *   onClick={() => navigate('/project/123/log/456/chat')}
 * />
 * ```
 */
export const ConversationCard: React.FC<ConversationCardProps> = ({
  conversation,
  onClick,
  className = ''
}) => {
  const { projectName, logDescription, unreadCount } = conversation;

  // Format time display
  const formatTimeAgo = () => {
    // TODO: Add lastActivity timestamp to conversation data
    // For now, just show unread count
    return `${unreadCount} ulest${unreadCount !== 1 ? 'e' : ''}`;
  };

  return (
    <div
      onClick={onClick}
      className={`
        card-modern hover-lift cursor-pointer transition-all duration-200
        border-l-4 border-l-jobblogg-primary
        ${className}
      `.trim().replace(/\s+/g, ' ')}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick();
        }
      }}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1 min-w-0">
          {/* Project and Log Info */}
          <div className="flex items-center gap-2 mb-2">
            <h3 className="font-semibold text-jobblogg-text-strong truncate">
              {projectName}
            </h3>
            <span className="text-jobblogg-text-muted">•</span>
            <span className="text-sm text-jobblogg-text-medium truncate">
              {logDescription}
            </span>
          </div>

          {/* Conversation Preview */}
          <div className="flex items-center gap-2">
            <svg className="w-4 h-4 text-jobblogg-text-muted flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <span className="text-sm text-jobblogg-text-muted">
              {formatTimeAgo()}
            </span>
          </div>
        </div>

        {/* Unread Badge and Arrow */}
        <div className="flex items-center gap-3 flex-shrink-0">
          {/* Unread Count Badge */}
          {unreadCount > 0 && (
            <div className="bg-jobblogg-warning text-white text-xs font-bold px-2 py-1 rounded-full min-w-[1.5rem] text-center">
              {unreadCount > 99 ? '99+' : unreadCount}
            </div>
          )}

          {/* Navigation Arrow */}
          <svg 
            className="w-5 h-5 text-jobblogg-text-muted group-hover:text-jobblogg-primary transition-colors" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </div>
      </div>
    </div>
  );
};
