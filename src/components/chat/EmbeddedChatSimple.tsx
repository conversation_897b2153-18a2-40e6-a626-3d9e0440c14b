import React, { useState } from 'react';

interface EmbeddedChatSimpleProps {
  logId: string;
  userId: string;
  userRole: 'contractor' | 'customer';
  className?: string;
}

/**
 * Simplified version of EmbeddedChatContainer to test basic functionality
 */
export const EmbeddedChatSimple: React.FC<EmbeddedChatSimpleProps> = ({
  logId,
  userId,
  userRole,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  console.log('🔧 EmbeddedChatSimple rendering:', { logId, userId, userRole });
  
  return (
    <div className={`border border-blue-500 rounded-lg bg-white ${className}`}>
      {/* Chat Header */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between p-3 hover:bg-blue-50 transition-colors duration-200 rounded-t-lg"
      >
        <div className="flex items-center gap-2">
          <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          <span className="text-sm font-medium text-blue-800">
            💬 Chat ({userRole === 'contractor' ? 'Leverandør' : 'Kunde'})
          </span>
        </div>
        <svg 
          className={`w-4 h-4 text-blue-600 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      
      {/* Chat Content */}
      {isExpanded && (
        <div className="p-3 border-t border-blue-200">
          <div className="text-sm text-blue-700 space-y-2">
            <div><strong>Status:</strong> ✅ Simple chat component working!</div>
            <div><strong>Log ID:</strong> {logId}</div>
            <div><strong>User ID:</strong> {userId}</div>
            <div><strong>Role:</strong> {userRole}</div>
          </div>
          <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded">
            <div className="text-xs text-blue-600">
              This simplified component proves the integration works. 
              If you see this, the issue is likely in the full EmbeddedChatContainer component.
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
