import React from 'react';

interface EmbeddedChatDebugProps {
  logId: string;
  userId: string;
  userRole: 'contractor' | 'customer';
  className?: string;
}

/**
 * Debug version of EmbeddedChatContainer to test basic rendering
 */
export const EmbeddedChatDebug: React.FC<EmbeddedChatDebugProps> = ({
  logId,
  userId,
  userRole,
  className = ''
}) => {
  console.log('🐛 EmbeddedChatDebug rendering with:', { logId, userId, userRole });

  const [isExpanded, setIsExpanded] = React.useState(false);

  return (
    <div className={`border-2 border-red-500 bg-red-50 rounded-lg ${className}`}>
      {/* Debug Header */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between p-3 hover:bg-red-100 transition-colors duration-200 rounded-t-lg"
      >
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-red-500 rounded-full"></div>
          <span className="text-sm font-medium text-red-800">
            🐛 DEBUG: Embedded Chat ({userRole})
          </span>
        </div>
        <svg
          className={`w-4 h-4 text-red-600 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Debug Content */}
      {isExpanded && (
        <div className="p-3 border-t border-red-300">
          <div className="text-sm text-red-700 space-y-1 mb-3">
            <div><strong>Log ID:</strong> {logId}</div>
            <div><strong>User ID:</strong> {userId || 'NOT PROVIDED'}</div>
            <div><strong>User Role:</strong> {userRole}</div>
            <div><strong>Timestamp:</strong> {new Date().toLocaleTimeString()}</div>
          </div>
          <div className="p-2 bg-white border border-red-300 rounded">
            <div className="text-xs text-red-600">
              ✅ Integration point is working! Issues might be:
              <ul className="list-disc list-inside mt-1">
                <li>EmbeddedChatContainer component not rendering</li>
                <li>Convex backend connection issues</li>
                <li>Missing log entries or user authentication</li>
                <li>CSS styling hiding the component</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
