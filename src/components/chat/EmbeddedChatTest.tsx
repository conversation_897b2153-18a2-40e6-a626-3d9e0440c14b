import React from 'react';

interface EmbeddedChatTestProps {
  logId: string;
  userId: string;
  userRole: string;
  className?: string;
}

export const EmbeddedChatTest: React.FC<EmbeddedChatTestProps> = ({
  logId,
  userId,
  userRole,
  className = ''
}) => {
  console.log('🚨 EmbeddedChatTest CALLED!', { logId, userId, userRole });

  return (
    <div className={`bg-red-500 text-white p-4 rounded border-4 border-black ${className}`}>
      <h3 className="font-bold text-lg mb-2">🚨 EMBEDDED CHAT TEST COMPONENT!</h3>
      <div className="space-y-1 text-sm">
        <p><strong>logId:</strong> {logId}</p>
        <p><strong>userId:</strong> {userId}</p>
        <p><strong>userRole:</strong> {userRole}</p>
      </div>
      <div className="mt-3 p-2 bg-red-600 rounded">
        <p className="text-xs">If you can see this, the component integration is working!</p>
      </div>
    </div>
  );
};
