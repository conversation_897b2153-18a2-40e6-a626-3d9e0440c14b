import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import type { Id } from '../../../convex/_generated/dataModel';
import { EmbeddedChatContainerProps, MessageFormData, ChatError, TypingIndicator, OptimisticMessage, MessageWithDisplayInfo } from '../../types/chat';
import { MessageList } from './MessageList';
import { MessageInput } from './MessageInput';
import { TypingIndicator as TypingIndicatorComponent } from './TypingIndicator';
import { ConnectionStatus } from './ConnectionStatus';
import { ScreenReaderAnnouncer, ChatScreenReaderContext, useScreenReaderAnnouncements } from './ScreenReaderAnnouncer';

// Global state to track active embedded chats for performance optimization
const activeEmbeddedChats = new Set<string>();
const MAX_CONCURRENT_EXPANDED_CHATS = 3;

/**
 * EmbeddedChatContainer - A compact, collapsible chat interface designed for embedding within log entry cards
 * 
 * Features:
 * - Collapsible/expandable interface
 * - Compact design optimized for card integration
 * - Full chat functionality (messages, reactions, typing indicators)
 * - Mobile-responsive design
 * - Accessibility support
 */
export const EmbeddedChatContainer: React.FC<EmbeddedChatContainerProps> = ({
  logId,
  userId,
  userRole,
  className = '',
  isInitiallyExpanded = false,
  maxHeight = '400px',
  showHeader = true,
  compactMode = true
}) => {
  // Generate unique chat instance ID
  const chatInstanceId = useMemo(() => `${logId}-${userId}`, [logId, userId]);

  // Expansion state with performance optimization
  const [isExpanded, setIsExpanded] = useState(isInitiallyExpanded);

  // Performance optimization: Track active chats
  useEffect(() => {
    if (isExpanded) {
      // Check if we're exceeding the limit
      if (activeEmbeddedChats.size >= MAX_CONCURRENT_EXPANDED_CHATS && !activeEmbeddedChats.has(chatInstanceId)) {
        console.warn(`JobbLogg: Maximum ${MAX_CONCURRENT_EXPANDED_CHATS} embedded chats can be expanded simultaneously for optimal performance`);
      }
      activeEmbeddedChats.add(chatInstanceId);
    } else {
      activeEmbeddedChats.delete(chatInstanceId);
    }

    return () => {
      activeEmbeddedChats.delete(chatInstanceId);
    };
  }, [isExpanded, chatInstanceId]);

  // Chat state
  const [error, setError] = useState<ChatError | null>(null);
  const [replyingTo, setReplyingTo] = useState<Id<"messages"> | null>(null);
  const [optimisticMessages, setOptimisticMessages] = useState<OptimisticMessage[]>([]);
  const [typingUsers, setTypingUsers] = useState<TypingIndicator[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Refs for scroll management
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Convex hooks - only fetch when expanded for performance
  // Additional optimization: Skip if too many chats are active
  const shouldFetchData = isExpanded && activeEmbeddedChats.size <= MAX_CONCURRENT_EXPANDED_CHATS;

  const messagesData = useQuery(
    api.messages.getMessagesWithDisplayNames,
    shouldFetchData ? {
      logId,
      userId,
      userRole,
      limit: 15, // Further reduced limit for multiple instances
      cursor: undefined
    } : "skip"
  );

  const sendMessage = useMutation(api.messages.sendMessage);
  const markAsRead = useMutation(api.messages.markAsRead);
  const addReaction = useMutation(api.messages.addReaction);
  const removeReaction = useMutation(api.messages.removeReaction);

  // Screen reader announcements
  const { announceMessage, announceError, announceTyping } = useScreenReaderAnnouncements();

  // Process messages with optimistic updates
  const allMessages = useMemo(() => {
    const serverMessages = messagesData?.messages || [];
    const optimisticMessagesWithDisplay: MessageWithDisplayInfo[] = optimisticMessages.map(msg => ({
      ...msg,
      senderDisplayName: userRole === 'contractor' ? 'Leverandør' : 'Kunde',
      isOwnMessage: true,
      replies: []
    }));
    
    return [...serverMessages, ...optimisticMessagesWithDisplay]
      .sort((a, b) => a.createdAt - b.createdAt);
  }, [messagesData?.messages, optimisticMessages, userRole]);

  // Get unread count for collapsed state
  const unreadCount = useMemo(() => {
    if (!messagesData?.messages) return 0;
    return messagesData.messages.filter(msg => 
      !msg.isOwnMessage && !msg.readBy?.includes(userId)
    ).length;
  }, [messagesData?.messages, userId]);

  // Auto-scroll to bottom when new messages arrive (debounced for performance)
  useEffect(() => {
    if (isExpanded && messagesEndRef.current) {
      const timeoutId = setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100); // Debounce scroll updates

      return () => clearTimeout(timeoutId);
    }
  }, [allMessages, isExpanded]);

  // Handle message sending
  const handleSendMessage = useCallback(async (formData: MessageFormData) => {
    if (!formData.text.trim()) return;

    const optimisticId = `optimistic-${Date.now()}` as Id<"messages">;
    const optimisticMessage: OptimisticMessage = {
      _id: optimisticId,
      logId,
      senderId: userId,
      senderRole: userRole,
      text: formData.text,
      createdAt: Date.now(),
      isDelivered: false,
      readBy: [],
      reactions: [],
      attachments: [],
      isDeleted: false,
      parentMessageId: replyingTo,
      editHistory: []
    };

    // Add optimistic message
    setOptimisticMessages(prev => [...prev, optimisticMessage]);
    setReplyingTo(null);

    try {
      setError(null);
      await sendMessage({
        logId,
        senderId: userId,
        senderRole: userRole,
        text: formData.text,
        parentMessageId: replyingTo || undefined,
        attachments: formData.attachments || []
      });

      // Remove optimistic message on success
      setOptimisticMessages(prev => prev.filter(msg => msg._id !== optimisticId));
      announceMessage('Melding sendt');
    } catch (err) {
      // Remove optimistic message on error
      setOptimisticMessages(prev => prev.filter(msg => msg._id !== optimisticId));
      setError({
        type: 'send_failed',
        message: err instanceof Error ? err.message : 'Kunne ikke sende melding',
        timestamp: Date.now()
      });
      announceError('Kunne ikke sende melding');
    }
  }, [sendMessage, logId, userId, userRole, replyingTo, announceMessage, announceError]);

  // Handle reaction toggle
  const handleReactionToggle = useCallback(async (messageId: Id<"messages">, emoji: string) => {
    try {
      setError(null);
      const message = allMessages.find(m => m._id === messageId);
      const hasReacted = message?.reactions.some(r => r.userId === userId && r.emoji === emoji);

      if (hasReacted) {
        await removeReaction({ messageId, userId, emoji });
      } else {
        await addReaction({ messageId, userId, emoji, userRole });
      }
    } catch (err) {
      setError({
        type: 'unknown',
        message: err instanceof Error ? err.message : 'Kunne ikke reagere på melding',
        timestamp: Date.now()
      });
    }
  }, [addReaction, removeReaction, userId, userRole, allMessages]);

  // Toggle expansion
  const toggleExpansion = useCallback(() => {
    setIsExpanded(prev => !prev);
    
    // Mark messages as read when expanding
    if (!isExpanded && unreadCount > 0) {
      setTimeout(() => {
        markAsRead({ logId, userId });
      }, 500);
    }
  }, [isExpanded, unreadCount, markAsRead, logId, userId]);

  // Debug logging
  console.log('🔧 EmbeddedChatContainer render:', {
    logId: logId,
    userId: userId,
    userRole: userRole,
    isExpanded: isExpanded,
    showHeader: showHeader,
    shouldFetchData: shouldFetchData,
    messagesData: messagesData ? 'loaded' : 'not loaded',
    allMessagesCount: allMessages.length,
    className: className
  });

  // ULTIMATE DEBUG TEST - Return ONLY the red box
  return (
    <div
      style={{
        position: 'fixed',
        top: '50px',
        left: '50px',
        width: '500px',
        height: '200px',
        backgroundColor: 'red',
        color: 'white',
        fontSize: '24px',
        fontWeight: 'bold',
        textAlign: 'center',
        padding: '20px',
        zIndex: 99999,
        border: '10px solid black',
        display: 'block'
      }}
    >
      🚨 EMBEDDED CHAT IS HERE! 🚨
      <br />
      logId: {logId}
      <br />
      userRole: {userRole}
      <br />
      Time: {new Date().toLocaleTimeString()}
    </div>
  );
};
