import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import type { Id } from '../../../convex/_generated/dataModel';
import { EmbeddedChatContainerProps, MessageFormData, ChatError, TypingIndicator, OptimisticMessage, MessageWithDisplayInfo } from '../../types/chat';
import { MessageList } from './MessageList';
import { MessageInput } from './MessageInput';
import { TypingIndicator as TypingIndicatorComponent } from './TypingIndicator';
import { ConnectionStatus } from './ConnectionStatus';
import { ScreenReaderAnnouncer, ChatScreenReaderContext, useScreenReaderAnnouncements } from './ScreenReaderAnnouncer';

// Global state to track active embedded chats for performance optimization
const activeEmbeddedChats = new Set<string>();
const MAX_CONCURRENT_EXPANDED_CHATS = 3;

/**
 * EmbeddedChatContainer - A compact, collapsible chat interface designed for embedding within log entry cards
 * 
 * Features:
 * - Collapsible/expandable interface
 * - Compact design optimized for card integration
 * - Full chat functionality (messages, reactions, typing indicators)
 * - Mobile-responsive design
 * - Accessibility support
 */
export const EmbeddedChatContainer: React.FC<EmbeddedChatContainerProps> = ({
  logId,
  userId,
  userRole,
  className = '',
  isInitiallyExpanded = false,
  maxHeight = '400px',
  showHeader = true,
  compactMode = true
}) => {
  // Generate unique chat instance ID
  const chatInstanceId = useMemo(() => `${logId}-${userId}`, [logId, userId]);

  // Expansion state with performance optimization
  const [isExpanded, setIsExpanded] = useState(isInitiallyExpanded);

  // Performance optimization: Track active chats
  useEffect(() => {
    if (isExpanded) {
      // Check if we're exceeding the limit
      if (activeEmbeddedChats.size >= MAX_CONCURRENT_EXPANDED_CHATS && !activeEmbeddedChats.has(chatInstanceId)) {
        console.warn(`JobbLogg: Maximum ${MAX_CONCURRENT_EXPANDED_CHATS} embedded chats can be expanded simultaneously for optimal performance`);
      }
      activeEmbeddedChats.add(chatInstanceId);
    } else {
      activeEmbeddedChats.delete(chatInstanceId);
    }

    return () => {
      activeEmbeddedChats.delete(chatInstanceId);
    };
  }, [isExpanded, chatInstanceId]);

  // Chat state
  const [error, setError] = useState<ChatError | null>(null);
  const [replyingTo, setReplyingTo] = useState<Id<"messages"> | null>(null);
  const [optimisticMessages, setOptimisticMessages] = useState<OptimisticMessage[]>([]);
  const [typingUsers, setTypingUsers] = useState<TypingIndicator[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Refs for scroll management
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Convex hooks - only fetch when expanded for performance
  // Additional optimization: Skip if too many chats are active
  const shouldFetchData = isExpanded && activeEmbeddedChats.size <= MAX_CONCURRENT_EXPANDED_CHATS;

  const messagesData = useQuery(
    api.messages.getMessagesWithDisplayNames,
    shouldFetchData ? {
      logId,
      userId,
      userRole,
      limit: 15, // Further reduced limit for multiple instances
      cursor: undefined
    } : "skip"
  );

  const sendMessage = useMutation(api.messages.sendMessage);
  const markAsRead = useMutation(api.messages.markAsRead);
  const addReaction = useMutation(api.messages.addReaction);
  const removeReaction = useMutation(api.messages.removeReaction);

  // Screen reader announcements
  const { announceMessage, announceError, announceTyping } = useScreenReaderAnnouncements();

  // Process messages with optimistic updates
  const allMessages = useMemo(() => {
    const serverMessages = messagesData?.messages || [];
    const optimisticMessagesWithDisplay: MessageWithDisplayInfo[] = optimisticMessages.map(msg => ({
      ...msg,
      senderDisplayName: userRole === 'contractor' ? 'Leverandør' : 'Kunde',
      isOwnMessage: true,
      replies: []
    }));
    
    return [...serverMessages, ...optimisticMessagesWithDisplay]
      .sort((a, b) => a.createdAt - b.createdAt);
  }, [messagesData?.messages, optimisticMessages, userRole]);

  // Get unread count for collapsed state
  const unreadCount = useMemo(() => {
    if (!messagesData?.messages) return 0;
    return messagesData.messages.filter(msg => 
      !msg.isOwnMessage && !msg.readBy?.includes(userId)
    ).length;
  }, [messagesData?.messages, userId]);

  // Auto-scroll to bottom when new messages arrive (debounced for performance)
  useEffect(() => {
    if (isExpanded && messagesEndRef.current) {
      const timeoutId = setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100); // Debounce scroll updates

      return () => clearTimeout(timeoutId);
    }
  }, [allMessages, isExpanded]);

  // Handle message sending
  const handleSendMessage = useCallback(async (formData: MessageFormData) => {
    if (!formData.text.trim()) return;

    const optimisticId = `optimistic-${Date.now()}` as Id<"messages">;
    const optimisticMessage: OptimisticMessage = {
      _id: optimisticId,
      logId,
      senderId: userId,
      senderRole: userRole,
      text: formData.text,
      createdAt: Date.now(),
      isDelivered: false,
      readBy: [],
      reactions: [],
      attachments: [],
      isDeleted: false,
      parentMessageId: replyingTo,
      editHistory: []
    };

    // Add optimistic message
    setOptimisticMessages(prev => [...prev, optimisticMessage]);
    setReplyingTo(null);

    try {
      setError(null);
      await sendMessage({
        logId,
        senderId: userId,
        senderRole: userRole,
        text: formData.text,
        parentMessageId: replyingTo || undefined,
        attachments: formData.attachments || []
      });

      // Remove optimistic message on success
      setOptimisticMessages(prev => prev.filter(msg => msg._id !== optimisticId));
      announceMessage('Melding sendt');
    } catch (err) {
      // Remove optimistic message on error
      setOptimisticMessages(prev => prev.filter(msg => msg._id !== optimisticId));
      setError({
        type: 'send_failed',
        message: err instanceof Error ? err.message : 'Kunne ikke sende melding',
        timestamp: Date.now()
      });
      announceError('Kunne ikke sende melding');
    }
  }, [sendMessage, logId, userId, userRole, replyingTo, announceMessage, announceError]);

  // Handle reaction toggle
  const handleReactionToggle = useCallback(async (messageId: Id<"messages">, emoji: string) => {
    try {
      setError(null);
      const message = allMessages.find(m => m._id === messageId);
      const hasReacted = message?.reactions.some(r => r.userId === userId && r.emoji === emoji);

      if (hasReacted) {
        await removeReaction({ messageId, userId, emoji });
      } else {
        await addReaction({ messageId, userId, emoji, userRole });
      }
    } catch (err) {
      setError({
        type: 'unknown',
        message: err instanceof Error ? err.message : 'Kunne ikke reagere på melding',
        timestamp: Date.now()
      });
    }
  }, [addReaction, removeReaction, userId, userRole, allMessages]);

  // Toggle expansion
  const toggleExpansion = useCallback(() => {
    setIsExpanded(prev => !prev);
    
    // Mark messages as read when expanding
    if (!isExpanded && unreadCount > 0) {
      setTimeout(() => {
        markAsRead({ logId, userId });
      }, 500);
    }
  }, [isExpanded, unreadCount, markAsRead, logId, userId]);

  return (
    <ChatScreenReaderContext>
      <div
        ref={containerRef}
        className={`embedded-chat-container border border-jobblogg-border rounded-lg bg-white transition-all duration-300 ${className}`}
        role="region"
        aria-label="Innebygd chat"
      >
        {/* Chat Header - Collapsible trigger */}
        {showHeader && (
          <button
            onClick={toggleExpansion}
            className="w-full flex items-center justify-between p-3 sm:p-4 hover:bg-jobblogg-neutral-soft transition-colors duration-200 rounded-t-lg touch-manipulation min-h-[44px]"
            aria-expanded={isExpanded}
            aria-controls="embedded-chat-content"
          >
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <span className="text-sm font-medium text-jobblogg-text-strong">
                {userRole === 'contractor' ? 'Diskuter med kunde' : 'Diskuter med kontraktør'}
              </span>
              {unreadCount > 0 && (
                <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-jobblogg-error rounded-full">
                  {unreadCount}
                </span>
              )}
            </div>
            <svg 
              className={`w-4 h-4 text-jobblogg-text-muted transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        )}

        {/* Chat Content - Expandable */}
        <div
          id="embedded-chat-content"
          className={`transition-all duration-300 overflow-hidden ${
            isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
          }`}
          style={{ maxHeight: isExpanded ? maxHeight : '0' }}
        >
          {isExpanded && (
            <>
              {/* Connection Status */}
              <div className="px-2 sm:px-3 py-1 border-b border-jobblogg-border">
                <ConnectionStatus />
              </div>

              {/* Messages Area */}
              <div className="flex flex-col h-64 sm:h-80">
                {/* Messages List */}
                <div className="flex-1 overflow-y-auto p-2 sm:p-3 space-y-2">
                  {error && (
                    <div className="p-2 bg-jobblogg-error-soft text-jobblogg-error text-sm rounded border border-jobblogg-error-border">
                      {error.message}
                    </div>
                  )}
                  
                  {allMessages.length === 0 ? (
                    <div className="text-center py-8 text-jobblogg-text-muted">
                      <svg className="w-8 h-8 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                      <p className="text-sm">Ingen meldinger ennå</p>
                      <p className="text-xs mt-1">Start en samtale om denne loggføringen</p>
                    </div>
                  ) : (
                    <MessageList
                      messages={allMessages}
                      userId={userId}
                      userRole={userRole}
                      onReactionToggle={handleReactionToggle}
                      onReply={setReplyingTo}
                      onEdit={() => {}} // TODO: Implement edit in embedded mode
                      onDelete={() => {}} // TODO: Implement delete in embedded mode
                      compactMode={compactMode}
                    />
                  )}
                  
                  {/* Typing Indicator */}
                  {typingUsers.length > 0 && (
                    <TypingIndicatorComponent users={typingUsers} />
                  )}
                  
                  <div ref={messagesEndRef} />
                </div>

                {/* Message Input */}
                <div className="border-t border-jobblogg-border p-2 sm:p-3">
                  <MessageInput
                    onSendMessage={handleSendMessage}
                    replyingTo={replyingTo}
                    onCancelReply={() => setReplyingTo(null)}
                    disabled={!!error}
                    compactMode={compactMode}
                  />
                </div>
              </div>
            </>
          )}
        </div>
      </div>
      
      <ScreenReaderAnnouncer />
    </ChatScreenReaderContext>
  );
};
