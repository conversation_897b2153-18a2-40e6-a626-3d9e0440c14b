// Address API utilities for Norwegian addresses
// Combines Bring/Posten API, Kartverket API, and Google Places API

export interface PostalCodeResult {
  postalCode: string;
  city: string;
  municipality?: string;
  county?: string;
}

export interface AddressSuggestion {
  address: string;
  postalCode: string;
  city: string;
  fullAddress: string;
  source: 'kartverket' | 'google';
}

// Cache for postal codes to avoid repeated API calls
const postalCodeCache = new Map<string, PostalCodeResult>();
const addressCache = new Map<string, AddressSuggestion[]>();

/**
 * Get city name from postal code using Bring/Posten API
 */
export async function getPostalCodeInfo(postalCode: string): Promise<PostalCodeResult | null> {
  // Validate postal code format (4 digits)
  if (!/^\d{4}$/.test(postalCode)) {
    return null;
  }

  // Check cache first
  if (postalCodeCache.has(postalCode)) {
    return postalCodeCache.get(postalCode)!;
  }

  try {
    // Use Bring/Posten API for postal code lookup
    const response = await fetch(
      `https://api.bring.com/shippingguide/api/postalCode.json?clientUrl=jobblogg.no&pnr=${postalCode}`,
      {
        headers: {
          'Accept': 'application/json',
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Bring API error: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.valid && data.result) {
      const result: PostalCodeResult = {
        postalCode: postalCode,
        city: data.result,
        municipality: data.municipality,
        county: data.county,
      };
      
      // Cache the result
      postalCodeCache.set(postalCode, result);
      return result;
    }
    
    return null;
  } catch (error) {
    console.warn('Bring API failed, trying fallback:', error);
    
    // Fallback to a simple Norwegian postal code lookup
    return await getPostalCodeFallback(postalCode);
  }
}

/**
 * Fallback postal code lookup using a simple mapping
 */
async function getPostalCodeFallback(postalCode: string): Promise<PostalCodeResult | null> {
  // Common Norwegian postal codes for fallback
  const commonPostalCodes: Record<string, string> = {
    '0001': 'Oslo',
    '0010': 'Oslo',
    '0050': 'Oslo',
    '0150': 'Oslo',
    '0250': 'Oslo',
    '1001': 'Oslo',
    '1050': 'Oslo',
    '5001': 'Bergen',
    '5020': 'Bergen',
    '5050': 'Bergen',
    '7001': 'Trondheim',
    '7020': 'Trondheim',
    '7050': 'Trondheim',
    '4001': 'Stavanger',
    '4020': 'Stavanger',
    '4050': 'Stavanger',
    '9001': 'Tromsø',
    '9020': 'Tromsø',
    '8001': 'Bodø',
    '6001': 'Ålesund',
  };

  const city = commonPostalCodes[postalCode];
  if (city) {
    const result: PostalCodeResult = {
      postalCode,
      city,
    };
    postalCodeCache.set(postalCode, result);
    return result;
  }

  return null;
}

/**
 * Get address suggestions using Kartverket API (Norwegian addresses)
 */
export async function getAddressSuggestions(query: string): Promise<AddressSuggestion[]> {
  // Don't search for very short queries
  if (query.length < 3) {
    return [];
  }

  // Check cache first
  const cacheKey = query.toLowerCase().trim();
  if (addressCache.has(cacheKey)) {
    return addressCache.get(cacheKey)!;
  }

  try {
    // Use Kartverket API for Norwegian address suggestions
    const response = await fetch(
      `https://ws.geonorge.no/adresser/v1/sok?sok=${encodeURIComponent(query)}&treffPerSide=8&side=0`,
      {
        headers: {
          'Accept': 'application/json',
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Kartverket API error: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.adresser && Array.isArray(data.adresser)) {
      const suggestions: AddressSuggestion[] = data.adresser.map((addr: any) => ({
        address: `${addr.adressetekst}`,
        postalCode: addr.postnummer || '',
        city: addr.poststed || '',
        fullAddress: `${addr.adressetekst}, ${addr.postnummer} ${addr.poststed}`,
        source: 'kartverket' as const,
      }));

      // Cache the results
      addressCache.set(cacheKey, suggestions);
      return suggestions;
    }

    return [];
  } catch (error) {
    console.warn('Kartverket API failed, trying Google fallback:', error);
    
    // Fallback to Google Places API
    return await getGoogleAddressSuggestions(query);
  }
}

/**
 * Fallback address suggestions using Google Places API
 */
async function getGoogleAddressSuggestions(query: string): Promise<AddressSuggestion[]> {
  const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
  
  if (!apiKey) {
    console.warn('Google Maps API key not configured');
    return [];
  }

  try {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(query)}&components=country:no&types=address&key=${apiKey}`,
      {
        headers: {
          'Accept': 'application/json',
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Google Places API error: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.predictions && Array.isArray(data.predictions)) {
      const suggestions: AddressSuggestion[] = data.predictions.map((prediction: any) => {
        // Extract postal code and city from Google's structured formatting
        const description = prediction.description;
        const postalCodeMatch = description.match(/(\d{4})/);
        const parts = description.split(', ');
        
        return {
          address: parts[0] || description,
          postalCode: postalCodeMatch ? postalCodeMatch[1] : '',
          city: parts[parts.length - 2] || '',
          fullAddress: description,
          source: 'google' as const,
        };
      });

      return suggestions;
    }

    return [];
  } catch (error) {
    console.error('Google Places API failed:', error);
    return [];
  }
}

/**
 * Clear caches (useful for testing or memory management)
 */
export function clearAddressCaches(): void {
  postalCodeCache.clear();
  addressCache.clear();
}

/**
 * Debounce function for API calls
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
