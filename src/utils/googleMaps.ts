/**
 * Google Maps utility functions for JobbLogg
 * Handles address formatting, static map generation, and directions
 */

// Google Maps API configuration
const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '';

// Default contractor location (can be configured)
const DEFAULT_CONTRACTOR_LOCATION = 'Oslo, Norge';

/**
 * Format address from structured fields to a single string
 */
export const formatAddress = (
  streetAddress: string,
  postalCode: string,
  city: string,
  entrance?: string
): string => {
  const baseAddress = `${streetAddress}, ${postalCode} ${city}`;
  return entrance ? `${baseAddress} (${entrance})` : baseAddress;
};

/**
 * Format address for Google Maps API (plain text, not URL encoded)
 */
export const formatAddressForMaps = (
  streetAddress: string,
  postalCode: string,
  city: string
): string => {
  return `${streetAddress}, ${postalCode} ${city}, Norge`;
};

/**
 * Generate Google Maps static image URL
 */
export const generateStaticMapUrl = (
  streetAddress: string,
  postalCode: string,
  city: string,
  options: {
    width?: number;
    height?: number;
    zoom?: number;
    mapType?: 'roadmap' | 'satellite' | 'hybrid' | 'terrain';
  } = {}
): string => {
  const {
    width = 400,
    height = 300,
    zoom = 15,
    mapType = 'roadmap'
  } = options;

  const address = formatAddressForMaps(streetAddress, postalCode, city);

  const params = new URLSearchParams({
    center: address,
    zoom: zoom.toString(),
    size: `${width}x${height}`,
    maptype: mapType,
    markers: `color:red|${address}`,
    key: GOOGLE_MAPS_API_KEY
  });

  const url = `https://maps.googleapis.com/maps/api/staticmap?${params.toString()}`;

  // Debug logging
  console.log('Google Maps Static URL:', url);
  console.log('API Key configured:', Boolean(GOOGLE_MAPS_API_KEY));
  console.log('Address:', address);
  console.log('Map type:', mapType);

  return url;
};

/**
 * Generate Google Maps directions URL
 */
export const generateDirectionsUrl = (
  streetAddress: string,
  postalCode: string,
  city: string,
  origin: string = DEFAULT_CONTRACTOR_LOCATION
): string => {
  const destination = formatAddressForMaps(streetAddress, postalCode, city);
  const encodedOrigin = encodeURIComponent(origin);
  
  return `https://www.google.com/maps/dir/${encodedOrigin}/${destination}`;
};

/**
 * Validate if Google Maps API key is configured
 */
export const isGoogleMapsConfigured = (): boolean => {
  return Boolean(GOOGLE_MAPS_API_KEY && GOOGLE_MAPS_API_KEY.length > 0);
};

/**
 * Get fallback image URL when Google Maps is not available
 */
export const getFallbackMapImage = (): string => {
  return '/images/map-placeholder.svg';
};

/**
 * Check if address fields are complete for Maps integration
 */
export const isAddressComplete = (
  streetAddress?: string,
  postalCode?: string,
  city?: string
): boolean => {
  return Boolean(
    streetAddress && streetAddress.trim() &&
    postalCode && postalCode.trim() &&
    city && city.trim()
  );
};
