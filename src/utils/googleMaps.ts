/**
 * Google Maps utility functions for JobbLogg
 * Handles address formatting, static map generation, and directions
 */

// Google Maps API configuration
const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '';

// Default contractor location (can be configured)
const DEFAULT_CONTRACTOR_LOCATION = 'Oslo, Norge';

/**
 * Get optimal map type based on context
 * Optimized for contractor use cases
 */
const getOptimalMapType = (context?: 'projectCard' | 'addressPreview' | 'directions'): 'roadmap' | 'satellite' | 'hybrid' | 'terrain' => {
  switch (context) {
    case 'projectCard':
      return 'hybrid'; // Show both buildings and street names for quick identification
    case 'addressPreview':
      return 'hybrid'; // Detailed view for form validation
    case 'directions':
      return 'roadmap'; // Clear navigation with street names
    default:
      return 'hybrid'; // Best overall for contractors
  }
};

/**
 * Get optimal zoom level based on context
 */
const getOptimalZoom = (context?: 'projectCard' | 'addressPreview' | 'directions'): number => {
  switch (context) {
    case 'projectCard':
      return 16; // Good overview of building and surroundings
    case 'addressPreview':
      return 17; // Closer view for specific building identification
    case 'directions':
      return 15; // Neighborhood level for navigation context
    default:
      return 16; // Balanced view for contractors
  }
};

/**
 * Format address from structured fields to a single string
 */
export const formatAddress = (
  streetAddress: string,
  postalCode: string,
  city: string,
  entrance?: string
): string => {
  const baseAddress = `${streetAddress}, ${postalCode} ${city}`;
  return entrance ? `${baseAddress} (${entrance})` : baseAddress;
};

/**
 * Format address for Google Maps API (URL encoded)
 */
export const formatAddressForMaps = (
  streetAddress: string,
  postalCode: string,
  city: string
): string => {
  const address = `${streetAddress}, ${postalCode} ${city}, Norge`;
  return encodeURIComponent(address);
};

/**
 * Generate Google Maps static image URL
 * Optimized for contractor use cases with hybrid maps as default
 */
export const generateStaticMapUrl = (
  streetAddress: string,
  postalCode: string,
  city: string,
  options: {
    width?: number;
    height?: number;
    zoom?: number;
    mapType?: 'roadmap' | 'satellite' | 'hybrid' | 'terrain';
    context?: 'projectCard' | 'addressPreview' | 'directions';
  } = {}
): string => {
  const {
    width = 400,
    height = 300,
    zoom = getOptimalZoom(options.context),
    mapType = getOptimalMapType(options.context)
  } = options;

  const address = formatAddressForMaps(streetAddress, postalCode, city);
  
  const params = new URLSearchParams({
    center: address,
    zoom: zoom.toString(),
    size: `${width}x${height}`,
    maptype: mapType,
    markers: `color:red|${address}`,
    key: GOOGLE_MAPS_API_KEY
  });

  return `https://maps.googleapis.com/maps/api/staticmap?${params.toString()}`;
};

/**
 * Generate Google Maps directions URL
 */
export const generateDirectionsUrl = (
  streetAddress: string,
  postalCode: string,
  city: string,
  origin: string = DEFAULT_CONTRACTOR_LOCATION
): string => {
  const destination = formatAddressForMaps(streetAddress, postalCode, city);
  const encodedOrigin = encodeURIComponent(origin);
  
  return `https://www.google.com/maps/dir/${encodedOrigin}/${destination}`;
};

/**
 * Validate if Google Maps API key is configured
 */
export const isGoogleMapsConfigured = (): boolean => {
  return Boolean(GOOGLE_MAPS_API_KEY && GOOGLE_MAPS_API_KEY.length > 0);
};

/**
 * Get fallback image URL when Google Maps is not available
 */
export const getFallbackMapImage = (): string => {
  return '/images/map-placeholder.svg';
};

/**
 * Check if address fields are complete for Maps integration
 */
export const isAddressComplete = (
  streetAddress?: string,
  postalCode?: string,
  city?: string
): boolean => {
  return Boolean(
    streetAddress && streetAddress.trim() &&
    postalCode && postalCode.trim() &&
    city && city.trim()
  );
};
